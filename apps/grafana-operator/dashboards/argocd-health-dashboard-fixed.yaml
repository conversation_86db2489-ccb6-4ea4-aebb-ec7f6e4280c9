# This file defines a GrafanaDashboard Custom Resource (CR) for an ArgoCD Health Metrics dashboard.
# It is intended to be managed by the Grafana Operator and displayed in a specific Grafana instance.
#
# KEY CONFIGURATIONS:
# 1. 'metadata.labels': These labels are used by a Grafana CR's 'dashboardLabelSelector'
#    to determine if it should manage this GrafanaDashboard CR.
# 2. 'spec.instanceSelector': This determines WHICH Grafana instance(s) will display this dashboard.
#
# --- Interaction with 'grafana-reference' (manages 'grafana-dev' instance) ---
# 'grafana-reference' CR (in 'monitoring-dev') uses:
#   dashboardLabelSelector:
#   - matchExpressions:
#     - key: app # <--- Note: 'app', not 'app.kubernetes.io/name'
#       operator: In
#       values:
#       - grafana
#
# CURRENT 'metadata.labels' in this file:
#   app.kubernetes.io/name: grafana
#   app.kubernetes.io/instance: grafana-dev
#
# LABEL MISMATCH FOR SELECTION: This dashboard LACKS the 'app: grafana' label.
# Therefore, 'grafana-reference' will NOT select or manage this dashboard.
# To fix, add 'app: grafana' to the labels below.
#
# --- Instance Targeting ---
# CURRENT 'spec.instanceSelector' in this file:
#   app.kubernetes.io/instance: grafana-managed
#
# INSTANCE MISMATCH: This targets 'grafana-managed', NOT 'grafana-dev'.
# Even if selected by 'grafana-reference', it wouldn't appear on 'grafana-dev'.
# To fix, change 'grafana-managed' to 'grafana-dev' in 'spec.instanceSelector'.
# The "-fixed" in the filename suggests this was intended.
# It is intended to be managed by the Grafana Operator and displayed in a specific Grafana instance.
#
# NOTE: Pay close attention to 'metadata.labels' (for selection by a Grafana CR)
# and 'spec.instanceSelector' (to target the correct Grafana instance).
---
apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaDashboard
metadata:
  name: argocd-health-dashboard # Name of the dashboard CR.
  namespace: monitoring-dev # Namespace where this CR and the target Grafana CR exist.
  labels:
    # To be selected by 'grafana-reference', add 'app: grafana' here.
    app: grafana
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: grafana-dev
# spec:
#   instanceSelector:
#     This selector determines WHICH Grafana instance(s) will display this dashboard.
#     The labels here MUST match the labels of the target Grafana instance.
#
#     TARGET INSTANCE ('grafana-dev', managed by 'grafana-reference' CR):
#       Has labels like: 'app.kubernetes.io/instance: grafana-dev', 'app.kubernetes.io/name: grafana'.
#
#     CURRENT SELECTOR in this file (below):
#       'app.kubernetes.io/instance: grafana-managed'
#
#     CRITICAL MISMATCH: The current 'instanceSelector' targets 'grafana-managed',
#     NOT 'grafana-dev'. This dashboard will NOT appear in 'grafana-dev' with this configuration.
#     The "-fixed" in the filename 'argocd-health-dashboard-fixed.yaml' might imply
#     this selector was intended to be 'grafana-dev'. If so, it needs to be corrected.
spec:
  instanceSelector:
    matchLabels:
      app.kubernetes.io/name: grafana
      app.kubernetes.io/instance: grafana-dev-managed
  configMapRef:
    name: argocd-health-dashboard-json
    key: argocd-health-dashboard.json

