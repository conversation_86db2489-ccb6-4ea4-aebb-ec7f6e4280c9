apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaDashboard
metadata:
  name: argocd-dashboard
  namespace: monitoring-dev
  labels:
    app: grafana
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: grafana-dev
spec:
  instanceSelector:
    matchLabels:
      app.kubernetes.io/name: grafana
      app.kubernetes.io/instance: grafana-dev-managed
  json: |
    {
      "annotations": {
        "list": [
          {
            "builtIn": 1,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "enable": true,
            "hide": true,
            "iconColor": "rgba(0, 211, 255, 1)",
            "name": "Annotations & Alerts",
            "target": {
              "limit": 100,
              "matchAny": false,
              "tags": [],
              "type": "dashboard"
            },
            "type": "dashboard"
          }
        ]
      },
      "editable": true,
      "fiscalYearStartMonth": 0,
      "graphTooltip": 0,
      "id": null,
      "links": [],
      "liveNow": false,
      "panels": [
        {
          "datasource": {
            "type": "loki",
            "uid": "Loki"
          },
          "gridPos": {
            "h": 8,
            "w": 24,
            "x": 0,
            "y": 0
          },
          "id": 1,
          "options": {
            "dedupStrategy": "none",
            "enableLogDetails": true,
            "prettifyLogMessage": false,
            "showCommonLabels": false,
            "showLabels": false,
            "showTime": true,
            "sortOrder": "Descending",
            "wrapLogMessage": false
          },
          "targets": [
            {
              "datasource": {
                "type": "loki",
                "uid": "Loki"
              },
              "editorMode": "builder",
              "expr": "{namespace=\"argocd\"}",
              "queryType": "range",
              "refId": "A"
            }
          ],
          "title": "ArgoCD Logs",
          "type": "logs"
        },
        {
          "datasource": {
            "type": "loki",
            "uid": "Loki"
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 0,
            "y": 8
          },
          "id": 2,
          "options": {
            "dedupStrategy": "none",
            "enableLogDetails": true,
            "prettifyLogMessage": false,
            "showCommonLabels": false,
            "showLabels": false,
            "showTime": true,
            "sortOrder": "Descending",
            "wrapLogMessage": false
          },
          "targets": [
            {
              "datasource": {
                "type": "loki",
                "uid": "Loki"
              },
              "editorMode": "builder",
              "expr": "{namespace=\"argocd\"} |= \"error\"",
              "queryType": "range",
              "refId": "A"
            }
          ],
          "title": "ArgoCD Error Logs",
          "type": "logs"
        },
        {
          "datasource": {
            "type": "loki",
            "uid": "Loki"
          },
          "gridPos": {
            "h": 8,
            "w": 12,
            "x": 12,
            "y": 8
          },
          "id": 3,
          "options": {
            "dedupStrategy": "none",
            "enableLogDetails": true,
            "prettifyLogMessage": false,
            "showCommonLabels": false,
            "showLabels": false,
            "showTime": true,
            "sortOrder": "Descending",
            "wrapLogMessage": false
          },
          "targets": [
            {
              "datasource": {
                "type": "loki",
                "uid": "Loki"
              },
              "editorMode": "builder",
              "expr": "{namespace=\"argocd\"} |= \"sync\"",
              "queryType": "range",
              "refId": "A"
            }
          ],
          "title": "ArgoCD Sync Logs",
          "type": "logs"
        }
      ],
      "refresh": "5s",
      "schemaVersion": 38,
      "style": "dark",
      "tags": [
        "argocd",
        "kubernetes"
      ],
      "templating": {
        "list": []
      },
      "time": {
        "from": "now-1h",
        "to": "now"
      },
      "timepicker": {},
      "timezone": "",
      "title": "ArgoCD Dashboard",
      "uid": "argocd-simple",
      "version": 1,
      "weekStart": ""
    }
